import { InputDefinition, WorkflowNodeData } from "@/types";
import { Node } from "reactflow";
import { shouldShowInput } from "@/utils/visibility-rules";

/**
 * Determines if an input should be visible based on node type and configuration
 * @param inputDef The input definition
 * @param node The workflow node
 * @param config The current configuration
 * @returns True if the input should be visible, false otherwise
 */
export function checkInputVisibility(
  inputDef: InputDefinition,
  node: Node<WorkflowNodeData> | null,
  config: Record<string, any>
): boolean {
  if (!node) return false;

  // PROMINENT DEBUG: Log every call to this function for any node that might be MergeData related
  if (node.data.type?.includes("MergeData") || node.data.originalType?.includes("MergeData") || inputDef.name.startsWith("output_key_")) {
    console.log(`🔍 [VISIBILITY] Checking ${inputDef.name}`, {
      inputName: inputDef.name,
      nodeType: node.data.type,
      originalType: node.data.originalType,
      nodeId: node.id,
      config: config
    });
  }

  // Special handling for DynamicCombineTextComponent
  if (
    node.data.type === "DynamicCombineTextComponent" &&
    inputDef.name.startsWith("input_") &&
    !inputDef.is_handle
  ) {
    // Extract the index from the input name (e.g., "input_3" -> 3)
    const match = inputDef.name.match(/input_(\d+)/);
    if (match && match[1]) {
      const inputIndex = parseInt(match[1], 10);
      const numAdditionalInputs = parseInt(config.num_additional_inputs || "0", 10);

      // Show the input if its index is less than or equal to the number of additional inputs
      return inputIndex <= numAdditionalInputs;
    }
  }

  // Special handling for MergeDataComponent output keys
  if (
    (node.data.type === "MergeDataComponent" || node.data.originalType === "MergeDataComponent") &&
    inputDef.name.startsWith("output_key_")
  ) {
    // DEBUG: Log the visibility check
    console.log(`[DEBUG] MergeData visibility check for ${inputDef.name}:`, {
      merge_strategy: config.merge_strategy,
      num_additional_inputs: config.num_additional_inputs
    });

    // Only show output keys when merge_strategy is "Structured Compose"
    if (config.merge_strategy !== "Structured Compose") {
      console.log(`[DEBUG] ${inputDef.name} hidden: merge_strategy is not "Structured Compose"`);
      return false;
    }

    // Extract the index from the output key name (e.g., "output_key_3" -> 3)
    const match = inputDef.name.match(/output_key_(\d+)/);
    if (match && match[1]) {
      const keyIndex = parseInt(match[1], 10);
      const numAdditionalInputs = parseInt(config.num_additional_inputs || "0", 10);

      // Show output_key_1 (for main input) and output_key_2 through output_key_(1+numAdditionalInputs)
      // For example: if numAdditionalInputs = 3, show output_key_1, output_key_2, output_key_3, output_key_4
      const totalInputs = 1 + numAdditionalInputs; // main_input + additional inputs
      const shouldShow = keyIndex <= totalInputs;

      console.log(`[DEBUG] ${inputDef.name}: keyIndex=${keyIndex}, totalInputs=${totalInputs}, shouldShow=${shouldShow}`);
      return shouldShow;
    }

    // If we can't parse the key index, hide it
    console.log(`[DEBUG] ${inputDef.name} hidden: could not parse key index`);
    return false;
  }

  // UPDATED: Special handling for ConditionalNode with new dual-purpose naming
  if (node.data.originalType === "ConditionalNode") {
    // Handle "primary" input (condition 1) - always visible
    if (inputDef.name === "primary") {
      return true;
    }

    // Handle "condition_X" inputs (conditions 2+) - new dual-purpose naming
    if (inputDef.name.startsWith("condition_") && inputDef.name.match(/^condition_\d+$/)) {
      const match = inputDef.name.match(/condition_(\d+)/);
      if (match && match[1]) {
        const conditionIndex = parseInt(match[1], 10);
        const numAdditionalConditions = parseInt(config.num_additional_conditions || "0", 10);
        const totalConditions = 1 + numAdditionalConditions; // Base 1 + additional

        // Show if condition index is within total conditions
        return conditionIndex <= totalConditions;
      }
    }

    // Handle dynamic condition configuration inputs (condition_2_*, condition_3_*, etc.)
    const conditionMatch = inputDef.name.match(/condition_(\d+)_/);
    if (conditionMatch && conditionMatch[1]) {
      const conditionIndex = parseInt(conditionMatch[1], 10);
      const numAdditionalConditions = parseInt(config.num_additional_conditions || "0", 10);
      const totalConditions = 1 + numAdditionalConditions;

      // For conditions 2 and above, check if they should be visible
      if (conditionIndex > 1) {
        // Show if condition index is within total conditions
        return conditionIndex <= totalConditions;
      }
    }
  }

  // Special handling for MCP Marketplace components
  if (isMCPMarketplaceComponent(node)) {
    return checkMCPMarketplaceInputVisibility(inputDef, node, config);
  }

  // Special handling for MCP Tools component
  if (node.data.type === "MCPToolsComponent") {
    return checkMCPToolsInputVisibility(inputDef, config);
  }

  // Use the utility function for standard visibility rules
  return shouldShowInput(inputDef, config);
}

/**
 * Checks if a node is an MCP Marketplace component
 */
function isMCPMarketplaceComponent(node: Node<WorkflowNodeData> | null): boolean {
  if (!node) return false;
  return node.data.type === "MCPMarketplaceComponent" ||
         (node.data.definition?.category === "MCP Marketplace");
}

/**
 * Checks visibility for MCP Marketplace component inputs
 */
function checkMCPMarketplaceInputVisibility(
  inputDef: InputDefinition,
  node: Node<WorkflowNodeData>,
  config: Record<string, any>
): boolean {
  // For explicit handle inputs (ending with _handle), always show them
  if (inputDef.input_type === "handle" || inputDef.name.endsWith("_handle")) {
    // Always show explicit handle inputs
    return true;
  }

  // Hide connection fields that have a direct input equivalent
  if (inputDef.name.endsWith("_connection")) {
    // Check if there's a direct input with the same base name
    const baseName = inputDef.name.replace("_connection", "");
    const hasDirectInput =
      node.data?.definition?.inputs?.some((input) => input.name === baseName) || false;
    if (hasDirectInput) {
      return false;
    }
  }

  // For inputs with is_handle=true, always show them
  if (inputDef.is_handle) {
    return true;
  }

  // For regular inputs, check if there's a corresponding handle
  if (hasCorrespondingHandle(inputDef.name, node)) {
    // If the handle is connected, hide the direct input
    // This would require the isInputConnected function from useConnectedHandles
    // For now, we'll return true and handle this in the component
    return true;
  }

  // Default to showing the input
  return true;
}

/**
 * Checks if an input has a corresponding handle
 */
function hasCorrespondingHandle(inputName: string, node: Node<WorkflowNodeData>): boolean {
  if (!node?.data?.definition?.inputs) return false;

  // Check for handle with a suffix pattern (e.g., input_dict_handle for input_dict)
  const handleSuffix = node.data.definition.inputs.find(
    (input) =>
      (input.is_handle || input.input_type === "handle") && input.name === `${inputName}_handle`,
  );

  return !!handleSuffix;
}

/**
 * Checks visibility for MCP Tools component inputs
 */
function checkMCPToolsInputVisibility(
  inputDef: InputDefinition,
  config: Record<string, any>
): boolean {
  const mode = config.mode || "Stdio"; // Default to Stdio if not set

  // For command and fetch_stdio_tools, only show when mode is Stdio
  if (inputDef.name === "command" || inputDef.name === "fetch_stdio_tools") {
    return mode === "Stdio";
  }

  // For sse_url and fetch_sse_tools, only show when mode is SSE
  if (inputDef.name === "sse_url" || inputDef.name === "fetch_sse_tools") {
    return mode === "SSE";
  }

  // For selected_tool_name, check connection_status
  if (inputDef.name === "selected_tool_name") {
    const connectionStatus = config.connection_status || "Not Connected";
    return connectionStatus === "Connected";
  }

  // For refresh_tools and disconnect buttons, always hide them
  if (inputDef.name === "refresh_tools" || inputDef.name === "disconnect") {
    return false;
  }

  // Use the utility function for standard visibility rules
  return shouldShowInput(inputDef, config);
}
